package id.grafindo.gsb.ui.exam

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.Context
import android.graphics.Color
import android.view.DragEvent
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import id.grafindo.gsb.R
import id.grafindo.gsb.data.remote.json.TipeSoal
import id.grafindo.gsb.databinding.FragmentExamQuestionBinding
import id.grafindo.gsb.domain.model.ExamNumber
import id.grafindo.gsb.domain.model.ExamQuestion
import timber.log.Timber

class ExamAnswerHandler(
    private val context: Context,
    private val layoutExam: FragmentExamQuestionBinding,
    private val questionRenderer: ExamQuestionRenderer,
    private val onAnswerChanged: (ExamNumber) -> Unit
) {
    private val selectedOptionMappings =
        mutableMapOf<Long, MutableList<Long>>() // Map<DropLabelId, List<OptionId>>

    private var selectedContainer: LinearLayout? = null
    private var selectedRadioButton: RadioButton? = null

    fun setupAnswerOptions(data: ExamQuestion) {
        questionRenderer.setupAnswerOptions(data)
    }

    fun updateActiveNumber(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        when (currentQuestion.tipeSoal) {
            TipeSoal.PilihanGanda -> setupSingleChoiceAnswer(examNumber, currentQuestion)
            TipeSoal.PilihanGandaKompleks -> setupMultipleChoiceAnswer(examNumber, currentQuestion)
            else -> setupMatchingAnswer(examNumber, currentQuestion)
        }
        setupDoubtButton(examNumber)
    }

    private fun setupSingleChoiceAnswer(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        val radioButtons = mutableListOf<RadioButton>()

        layoutExam.radioGroupChoices.apply {
            setOnCheckedChangeListener(null)
            clearCheck()
            setupPreviousChoice(examNumber, currentQuestion)

            // Collect RadioButtons from all children (direct or inside containers)
            for (i in 0 until childCount) {
                val radioButton = when (val view = getChildAt(i)) {
                    is RadioButton -> view
                    is ViewGroup -> view.children.find { it is RadioButton } as? RadioButton
                    else -> null
                }
                radioButton?.let { radioButtons.add(it) }
            }

            radioButtons.forEachIndexed { index, rb ->
                // Define the listener once so we can reuse it after clearing
                val listener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
                    if (isChecked) {
                        // Uncheck all other radio buttons
                        radioButtons.forEachIndexed { otherIndex, otherRb ->
                            if (index != otherIndex) {
                                otherRb.setOnCheckedChangeListener(null)
                                otherRb.isChecked = false
                                otherRb.setOnCheckedChangeListener(null) // prevent re-trigger
                                // Optionally restore listener to otherRb if needed
                            }
                        }

                        if (index in currentQuestion.dataJawaban.indices) {
                            val option = currentQuestion.dataJawaban[index].id.toString()
                            val newAnswer = examNumber.copy(pilihan = option)
                            onAnswerChanged(newAnswer)
                        }
                    }
                }

                rb.setOnCheckedChangeListener(null)
                rb.setOnCheckedChangeListener(listener)
            }
        }
    }

    private fun setupMultipleChoiceAnswer(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        layoutExam.checkboxGroupChoices.apply {
            setupPreviousChoice(examNumber, currentQuestion)

            val checkBoxes = mutableListOf<CheckBox>()

            // Collect all actual CheckBoxes (whether directly or inside containers)
            for (i in 0 until childCount) {
                when (val child = getChildAt(i)) {
                    is CheckBox -> checkBoxes.add(child)
                    is ViewGroup -> {
                        // Traverse children to find CheckBox
                        for (j in 0 until child.childCount) {
                            val nested = child.getChildAt(j)
                            if (nested is CheckBox) {
                                checkBoxes.add(nested)
                                break // Assume only one CheckBox per container
                            }
                        }
                    }
                }
            }

            // Now apply listeners to the collected CheckBoxes
            checkBoxes.forEachIndexed { index, checkBox ->
                checkBox.setOnCheckedChangeListener(null)
                checkBox.setOnCheckedChangeListener { _, isChecked ->
                    if (index in currentQuestion.dataJawaban.indices) {
                        val option = currentQuestion.dataJawaban[index].id.toString()
                        val currentChoices = examNumber.pilihan
                            .split(",")
                            .filter { it.isNotEmpty() }
                            .toMutableList()

                        if (isChecked) {
                            if (!currentChoices.contains(option)) currentChoices.add(option)
                        } else {
                            currentChoices.remove(option)
                        }

                        val newAnswer = examNumber.copy(pilihan = currentChoices.joinToString(","))
                        onAnswerChanged(newAnswer)
                    }
                }
            }
        }
    }

    private fun setupMatchingAnswer(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        selectedOptionMappings.clear()
        setupPreviousChoice(examNumber, currentQuestion)

        // Set up drag listeners for drop targets
        layoutExam.optionsContainer.apply {
            for (index in 0 until childCount) {
                val dropLayout = getChildAt(index) as LinearLayout
                val targetView = dropLayout.getChildAt(2) as TextView
                targetView.setOnDragListener { view, event ->
                    handleDragEvent(view, event, index, examNumber, currentQuestion)
                }
                targetView.setOnClickListener {
                    onTargetViewClick(dropLayout, targetView, examNumber)
                }
            }
        }

        // Set up drag listeners for option buttons
        layoutExam.targetsContainer.apply {
            for (index in 0 until childCount) {
                val optionLayout = getChildAt(index) as LinearLayout
                val button = optionLayout.getChildAt(0) as android.widget.Button
                val label = 'A' + index
                button.setOnLongClickListener { view ->
                    startDrag(view, label)
                    true
                }
            }
        }
    }

    private fun setupPreviousChoice(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        when (currentQuestion.tipeSoal) {
            TipeSoal.PilihanGanda -> setupSingleChoice(examNumber, currentQuestion)
            TipeSoal.PilihanGandaKompleks -> setupMultipleChoice(examNumber, currentQuestion)
            else -> setupMatchChoice(examNumber)
        }
    }

    private fun setupSingleChoice(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        layoutExam.radioGroupChoices.apply {
            var answerIndex = 0

            for (index in 0 until childCount) {
                val radioButton = when (val view = getChildAt(index)) {
                    is RadioButton -> view
                    is ViewGroup -> view.children.find { it is RadioButton } as? RadioButton
                    else -> null
                }

                radioButton?.let {
                    if (answerIndex < currentQuestion.dataJawaban.size) {
                        val option = currentQuestion.dataJawaban[answerIndex].id.toString()
                        it.isChecked = examNumber.pilihan == option
                        answerIndex++
                    }
                }
            }
        }
    }

    private fun setupMultipleChoice(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        val selectedIds = examNumber.pilihan.split(",").filter { it.isNotEmpty() }

        layoutExam.checkboxGroupChoices.apply {
            val checkBoxes = mutableListOf<CheckBox>()

            for (i in 0 until childCount) {
                when (val child = getChildAt(i)) {
                    is CheckBox -> checkBoxes.add(child)
                    is ViewGroup -> {
                        for (j in 0 until child.childCount) {
                            val nested = child.getChildAt(j)
                            if (nested is CheckBox) {
                                checkBoxes.add(nested)
                                break
                            }
                        }
                    }
                }
            }

            checkBoxes.forEachIndexed { index, checkBox ->
                val answerId = currentQuestion.dataJawaban.getOrNull(index)?.id?.toString()
                checkBox.isChecked = selectedIds.contains(answerId)
            }
        }
    }

    private fun setupMatchChoice(examNumber: ExamNumber) {
        if (examNumber.pilihan.isNotEmpty()) {
            examNumber.pilihan.split("],[").forEach { item ->
                val (dropLabelId, optionId) = item.removePrefix("[").removeSuffix("]")
                    .split(",")
                val dropLabelIdInt = dropLabelId.toLong()
                val optionIdInt = optionId.toLong()

                // Add the option to the selectedOptionMappings for this dropLabelId
                if (!selectedOptionMappings.containsKey(dropLabelIdInt)) {
                    selectedOptionMappings[dropLabelIdInt] = mutableListOf()
                }
                selectedOptionMappings[dropLabelIdInt]?.add(optionIdInt)
            }

            // After populating the selectedOptionMappings, update the UI
            updateUIWithSelections()
        }
    }

    private fun setupDoubtButton(examNumber: ExamNumber) {
        layoutExam.btnDoubt.apply {
            isChecked = examNumber.ragu
            setOnCheckedChangeListener { _, isChecked ->
                setBackgroundResource(if (isChecked) R.drawable.btn_doubt_alt else R.drawable.btn_doubt)
            }
            setOnClickListener {
                val newAnswer = examNumber.copy(ragu = isChecked)
                onAnswerChanged(newAnswer)
            }
        }
    }

    private fun updateSelectionState(newRadioButton: RadioButton) {
        selectedRadioButton?.isChecked = false
        selectedContainer?.isSelected = false

        selectedRadioButton = newRadioButton
        selectedContainer = newRadioButton.parent as? LinearLayout
        selectedContainer?.isSelected = true
    }

    @SuppressLint("NewApi")
    private fun startDrag(view: View, text: Char) {
        val clipData = ClipData.newPlainText("text", text.toString())
        val shadow = View.DragShadowBuilder(view)
        view.startDragAndDrop(clipData, shadow, view, 0)
        view.visibility = View.INVISIBLE
        Timber.d("Drag started for item: $text")
    }

    private fun handleDragEvent(
        view: View,
        event: DragEvent,
        index: Int,
        examNumber: ExamNumber,
        currentQuestion: ExamQuestion
    ): Boolean {
        when (event.action) {
            DragEvent.ACTION_DROP -> handleDropEvent(view, event, index, examNumber)
            DragEvent.ACTION_DRAG_ENDED -> handleDragEndEvent(event)
        }
        return true
    }

    private fun handleDropEvent(view: View, event: DragEvent, index: Int, examNumber: ExamNumber) {
        val draggedText = event.clipData.getItemAt(0).text.toString()
        val draggedOption = questionRenderer.optionTexts.find { it.text == draggedText.first() }

        draggedOption?.let {
            updateSelectedOptionIds(index, it)

            // Update UI dynamically based on stored selections
            updateTargetViewText(view as TextView, index)

            val option = addSelectedOption()
            val newAnswer = examNumber.copy(pilihan = option)
            onAnswerChanged(newAnswer)
        }
    }

    private fun handleDragEndEvent(event: DragEvent) {
        val draggedView = event.localState as View
        draggedView.visibility = View.VISIBLE
        Timber.d("Drag ended")
    }

    private fun onTargetViewClick(
        dropLayout: LinearLayout,
        targetView: TextView,
        examNumber: ExamNumber
    ) {
        val targetIndex = layoutExam.optionsContainer.indexOfChild(dropLayout)
        removeLastSelectedOption(targetIndex, targetView)

        val option = addSelectedOption()
        val newAnswer = examNumber.copy(pilihan = option)
        onAnswerChanged(newAnswer)
    }

    private fun addSelectedOption(): String {
        if (selectedOptionMappings.isEmpty()) return ""

        val formattedText = selectedOptionMappings.entries.flatMap { entry ->
            entry.value.map { value -> "[${entry.key},$value]" }
        }.joinToString(",")

        return formattedText
    }

    private fun removeLastSelectedOption(targetIndex: Int, targetView: TextView) {
        val dropLabelId = questionRenderer.dropItems[targetIndex].id

        // Check if there are answers to remove
        if (selectedOptionMappings[dropLabelId]?.isNotEmpty() == true) {
            selectedOptionMappings[dropLabelId]?.let {
                selectedOptionMappings[dropLabelId]?.removeAt(it.lastIndex)
            }
        }

        // Update UI dynamically
        updateTargetViewText(targetView, targetIndex)
    }

    private fun updateSelectedOptionIds(targetIndex: Int, draggedOption: Option) {
        val dropLabelId = questionRenderer.dropItems[targetIndex].id // Get the drop label ID

        // If there is no list yet, create one
        if (!selectedOptionMappings.containsKey(dropLabelId)) {
            selectedOptionMappings[dropLabelId] = mutableListOf()
        }

        // Add the new selection if it's not already present
        if (!selectedOptionMappings[dropLabelId]!!.contains(draggedOption.id)) {
            selectedOptionMappings[dropLabelId]!!.add(draggedOption.id)
        }
    }

    private fun updateTargetViewText(targetView: TextView, targetIndex: Int) {
        val dropLabelId = questionRenderer.dropItems[targetIndex].id
        val selectedOptions = selectedOptionMappings[dropLabelId] ?: emptyList()

        val newText = selectedOptions.joinToString("\n") { optionId ->
            questionRenderer.optionTexts.find { it.id == optionId }?.text.toString()
        }

        targetView.apply {
            text = newText
            textSize = 20f
            gravity = Gravity.CENTER
            // Set background depending on whether the text is empty or not
            background = ContextCompat.getDrawable(
                context,
                if (newText.isNotEmpty()) R.drawable.match_option_background else R.drawable.match_target_box
            )
            setTextColor(Color.WHITE)
        }
    }

    private fun updateUIWithSelections() {
        // Loop through the drop items (which correspond to the drop targets)
        questionRenderer.dropItems.forEachIndexed { index, dropItem ->
            val dropLabelId = dropItem.id
            val selectedOptions = selectedOptionMappings[dropLabelId] ?: emptyList()

            // Find the layout for this drop target in the options container
            val optionLayout =
                layoutExam.optionsContainer.getChildAt(index) as LinearLayout

            // Get the target TextView from this layout (it's the 3rd child in the LinearLayout based on your setup)
            val targetView =
                optionLayout.getChildAt(2) as TextView  // The target view is added as the 3rd view in the layout

            // Update the target view with the selected options
            val newText = selectedOptions.joinToString("\n") { optionId ->
                questionRenderer.optionTexts.find { it.id == optionId }?.text.toString()
            }

            targetView.apply {
                text = newText
                textSize = 20f
                gravity = Gravity.CENTER
                // Set background depending on whether the text is empty or not
                background = ContextCompat.getDrawable(
                    context,
                    if (newText.isNotEmpty()) R.drawable.match_option_background else R.drawable.match_target_box
                )
                setTextColor(Color.WHITE)
            }
        }
    }
}
