package id.grafindo.gsb.util

import android.content.Context
import id.grafindo.gsb.App
import okhttp3.OkHttpClient
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 * Centralized cache management for user isolation.
 * Ensures no data leakage between different user accounts.
 */
object CacheManager {
    
    /**
     * Clear all caches when user logs out.
     * This prevents data leakage between different user accounts.
     */
    fun clearAllCaches(context: Context) {
        Timber.d("Starting cache clearing process for user logout")
        
        try {
            // 1. Clear HTTP/Network cache
            clearNetworkCache(context)
            
            // 2. Clear in-memory caches will be handled by reloadDatabaseModule()
            // which recreates all ViewModels and repositories
            
            Timber.d("All caches cleared successfully")
        } catch (e: Exception) {
            Timber.e(e, "Error clearing caches during logout")
        }
    }
    
    /**
     * Clear HTTP/Network cache to prevent API response leakage.
     */
    private fun clearNetworkCache(context: Context) {
        try {
            // Get OkHttpClient from <PERSON><PERSON> and clear its cache
            val okHttpClient = App.instance.get<OkHttpClient>()
            okHttpClient.cache?.let { cache ->
                val cacheSize = cache.size()
                cache.evictAll()
                Timber.d("Network cache cleared: ${cacheSize} bytes freed")
            }
            
            // Also clear Android's built-in cache directory
            val cacheDir = context.cacheDir
            val cacheSizeBeforeMB = calculateDirectorySize(cacheDir) / (1024.0 * 1024.0)
            
            cacheDir.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    file.deleteRecursively()
                } else {
                    file.delete()
                }
            }
            
            val cacheSizeAfterMB = calculateDirectorySize(cacheDir) / (1024.0 * 1024.0)
            Timber.d("Android cache cleared: ${String.format("%.2f", cacheSizeBeforeMB - cacheSizeAfterMB)} MB freed")
            
        } catch (e: Exception) {
            Timber.e(e, "Error clearing network cache")
        }
    }
    
    /**
     * Calculate directory size in bytes.
     */
    private fun calculateDirectorySize(directory: java.io.File): Long {
        var size = 0L
        try {
            directory.listFiles()?.forEach { file ->
                size += if (file.isDirectory) {
                    calculateDirectorySize(file)
                } else {
                    file.length()
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "Error calculating directory size for ${directory.path}")
        }
        return size
    }
    
    /**
     * Clear specific app caches (can be called individually if needed).
     */
    fun clearAppSpecificCaches() {
        Timber.d("Clearing app-specific caches")
        
        // Add any app-specific cache clearing here
        // For example: Image cache, WebView cache, etc.
        
        // Force garbage collection to free up memory
        System.gc()
        
        Timber.d("App-specific caches cleared")
    }
}
