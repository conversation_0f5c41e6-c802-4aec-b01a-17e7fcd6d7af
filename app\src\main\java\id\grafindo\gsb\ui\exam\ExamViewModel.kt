package id.grafindo.gsb.ui.exam

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import id.grafindo.gsb.domain.model.Exam
import id.grafindo.gsb.domain.model.ExamNumber
import id.grafindo.gsb.domain.model.ExamProgress
import id.grafindo.gsb.data.remote.json.TipeSoal
import id.grafindo.gsb.domain.model.ExamQuestion
import id.grafindo.gsb.domain.usecase.ExamUseCase
import id.grafindo.gsb.ext.formatDuration
import id.grafindo.gsb.ext.toMillis
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.util.concurrent.TimeUnit

class ExamViewModel(
    bookId: Long,
    private val examId: Long,
    private val useCase: ExamUseCase,
) : ViewModel() {
    sealed class UIEvent {
        data object ExamReady : UIEvent()
        data object StartExamPrompt : UIEvent()
        data object ResumeExamPrompt : UIEvent()
        data object StopExam : UIEvent()
    }

    val book = useCase.getExamBookDetail(bookId).asLiveData()

    private val _indexQuestion = MutableLiveData<Int>()
    val indexQuestion: LiveData<Int> get() = _indexQuestion
    private val _activeQuestion = MediatorLiveData<ExamQuestion>()
    
    init {
        // Observe book changes to update questions when book is loaded
        _activeQuestion.addSource(book) { book ->
            if (book != null) {
                // When book is loaded, trigger question loading
                _indexQuestion.value?.let { currentIndex ->
                    loadQuestionForIndex(currentIndex - 1)
                }
            }
        }
        
        // Observe index changes to load questions when index changes
        _activeQuestion.addSource(indexQuestion) { index ->
            if (index > 0) {
                loadQuestionForIndex(index - 1)
            }
        }
    }
    
    private fun loadQuestionForIndex(questionIndex: Int) {
        // Check if we already have this question in cache
        _currentQuestions[questionIndex]?.let { question ->
            _activeQuestion.value = question
            return
        }
        
        // Get question ID or return if not found
        val questionId = _examQuestionIds.getOrNull(questionIndex) ?: run {
            _activeQuestion.value = ExamQuestion(
                id = 0,
                soal = "Soal tidak ditemukan",
                tipeSoal = TipeSoal.PilihanGanda,
                paketUjianId = 0,
                dataJawaban = emptyList(),
                jawabanmatchingright = emptyList()
            )
            return
        }
        
        // Get current book ID
        val currentBookId = book.value?.id
        if (currentBookId == null) {
            _activeQuestion.value = ExamQuestion(
                id = questionId,
                soal = "Memuat data ujian...",
                tipeSoal = TipeSoal.PilihanGanda,
                paketUjianId = 0,
                dataJawaban = emptyList(),
                jawabanmatchingright = emptyList()
            )
            return
        }
        
        // Load question in background
        viewModelScope.launch {
            try {
                val question = useCase.getQuestionById(currentBookId, examId, questionId)
                _currentQuestions[questionIndex] = question
                _activeQuestion.postValue(question)
            } catch (e: Exception) {
                Timber.e(e, "Error loading question $questionId")
                _activeQuestion.postValue(ExamQuestion(
                    id = questionId,
                    soal = "Gagal memuat soal: ${e.message}",
                    tipeSoal = TipeSoal.PilihanGanda,
                    paketUjianId = 0,
                    dataJawaban = emptyList(),
                    jawabanmatchingright = emptyList()
                ))
            }
        }
    }
    val activeQuestion: LiveData<ExamQuestion> = _activeQuestion
    val activeNumber: LiveData<ExamNumber> = indexQuestion.map { index ->
        try {
            if (index - 1 in mListExamNumber.indices) {
                mListExamNumber[index - 1]
            } else {
                // Jika index di luar jangkauan, buat ExamNumber baru dengan soalId yang benar
                val questionId = _examQuestionIds.getOrNull(index - 1) ?: 0L
                val newNumber = ExamNumber(
                    soalId = questionId,
                    nomorSoal = index,
                    pilihan = ""
                )

                // Pastikan list cukup besar dengan ExamNumber yang benar
                while (mListExamNumber.size <= index - 1) {
                    val missingIndex = mListExamNumber.size
                    val missingQuestionId = _examQuestionIds.getOrNull(missingIndex) ?: 0L
                    mListExamNumber.add(ExamNumber(
                        soalId = missingQuestionId,
                        nomorSoal = missingIndex + 1,
                        pilihan = ""
                    ))
                }

                // Update dengan ExamNumber yang benar
                mListExamNumber[index - 1] = newNumber
                newNumber
            }
        } catch (e: Exception) {
            Timber.e(e, "Error getting active number for index $index")
            // Bahkan untuk error case, gunakan soalId yang benar jika memungkinkan
            val questionId = _examQuestionIds.getOrNull((index ?: 1) - 1) ?: 0L
            ExamNumber(questionId, "", index ?: 1)
        }
    }
    private val eventChannel = Channel<UIEvent>(Channel.BUFFERED)
    val eventsFlow = eventChannel.receiveAsFlow()

    private val _listExamNumber = MutableSharedFlow<List<ExamNumber>>()
    val listExamNumber: LiveData<List<ExamNumber>> get() = _listExamNumber.asLiveData()

    private val _countDownText = MutableLiveData<String>()
    val countDownText: LiveData<String> get() = _countDownText

    private var mListExamNumber = ArrayList<ExamNumber>()
    private val _currentQuestions = mutableMapOf<Int, ExamQuestion>() // Cache untuk soal yang sudah di-load
    private val _examQuestionIds = mutableListOf<Long>() // Hanya menyimpan ID soal
    private lateinit var examData: Exam
    private lateinit var lastProgressData: ExamProgress
    private lateinit var timer: CountDownTimer

    /**
     * Clear all cached data for user isolation.
     * Called when user logs out to prevent data leakage between accounts.
     */
    fun clearCache() {
        Timber.d("Clearing ExamViewModel cache for user isolation")
        _currentQuestions.clear()
        _examQuestionIds.clear()
        mListExamNumber.clear()

        // Reset LiveData to initial state
        _activeQuestion.value = null
        _indexQuestion.value = 1
        _listExamNumber.value = emptyList()

        Timber.d("ExamViewModel cache cleared successfully")
    }
    private var elapsedTime = 0L

    init {
        viewModelScope.launch {
            examData = useCase.getExam(bookId, examId).last()
            setupExam()
        }
    }

    fun startExam() {
        timer = object : CountDownTimer(examData.waktu.toMillis(TimeUnit.MINUTES), 1000) {
            override fun onTick(millisUntilFinished: Long) {
                generateCountDownText(millisUntilFinished)
            }

            override fun onFinish() {
                stopExam()
            }
        }
        timer.start()
        saveProgress()
    }

    fun resumeExam() {
        Timber.d("sisa waktu : ${lastProgressData.timeLeft}")
        timer =
            object : CountDownTimer(lastProgressData.timeLeft, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    generateCountDownText(millisUntilFinished)
                }

                override fun onFinish() {
                    stopExam()
                }
            }
        timer.start()
        saveProgress()
    }

    fun finishExam() {
        timer.cancel()
        saveProgress()
    }

    private fun stopExam() {
        viewModelScope.launch {
            timer.cancel()
            eventChannel.send(UIEvent.StopExam)
        }
    }

    fun isTooFastExam(): Boolean {
        return elapsedTime > (examData.waktu.toMillis(TimeUnit.MINUTES) / 2)
    }

    private fun generateCountDownText(duration: Long) {
        val formattedDuration = formatDuration(duration)
        elapsedTime = duration
        _countDownText.postValue(formattedDuration)
    }

    private suspend fun loadQuestion(questionId: Long, index: Int) {
        try {
            val bookId = book.value?.id ?: return
            val question = useCase.getQuestionById(bookId, examId, questionId)
            _currentQuestions[index] = question
            // Trigger UI update
            _indexQuestion.postValue(_indexQuestion.value)
        } catch (e: Exception) {
            Timber.e(e, "Error loading question $questionId")
        }
    }

    private fun setupExam() {
        viewModelScope.launch {
            try {
                Timber.d("Setup exam started")
                Timber.d("Total questions: ${examData.soal.size}")
                
                // Clear existing data
                _examQuestionIds.clear()
                mListExamNumber.clear()
                
                // Simpan ID soal
                _examQuestionIds.addAll(examData.soal.map { it.id })
                Timber.d("Question IDs: $_examQuestionIds")
                android.util.Log.d("ExamViewModel", "Question IDs populated: ${_examQuestionIds.joinToString()}")
                
                // Inisialisasi mListExamNumber dengan data awal
                mListExamNumber.addAll(_examQuestionIds.mapIndexed { index, id ->
                    ExamNumber(
                        soalId = id,
                        nomorSoal = index + 1,
                        pilihan = ""
                    )
                })
                
                Timber.d("Initialized mListExamNumber with ${mListExamNumber.size} items")
                
                // Emit list yang sudah diisi
                _listExamNumber.emit(mListExamNumber.toList())
                Timber.d("Emitted listExamNumber")
                
                // Set index ke 1 (soal pertama)
                _indexQuestion.postValue(1)
                
                eventChannel.send(UIEvent.ExamReady)
                eventChannel.send(UIEvent.StartExamPrompt)
                Timber.d("Setup exam completed")
            } catch (e: Exception) {
                Timber.e(e, "Error in setupExam")
            }
        }
    }

    fun continueExam(newTime: Long) {
        viewModelScope.launch {
            lastProgressData = lastProgressData.copy(timeLeft = newTime)
            eventChannel.send(UIEvent.ExamReady)
            eventChannel.send(UIEvent.ResumeExamPrompt)
            _listExamNumber.emit(mListExamNumber)
        }
    }

    fun getExamName() = examData.namaPaket

    fun nextQuestion() {
        _indexQuestion.postValue(_indexQuestion.value?.plus(1))
        saveProgress()
    }

    fun prevQuestion() {
        _indexQuestion.postValue(_indexQuestion.value?.plus(-1))
        saveProgress()
    }

    fun jumpToQuestion(index: Int) {
        _indexQuestion.postValue(index)
        saveProgress()
    }

    fun markQuestion(data: ExamNumber) {
        android.util.Log.d("Exam", "markQuestion dipanggil - Soal: ${data.nomorSoal}, Pilihan: ${data.pilihan}, SoalId: ${data.soalId}")

        viewModelScope.launch {
            try {
                // Pastikan mListExamNumber sudah diinisialisasi dengan ukuran yang tepat
                if (mListExamNumber.size != _examQuestionIds.size && _examQuestionIds.isNotEmpty()) {
                    val newList = ArrayList<ExamNumber>(_examQuestionIds.size)
                    for (i in _examQuestionIds.indices) {
                        val existing = mListExamNumber.getOrNull(i)
                        newList.add(
                            existing ?: ExamNumber(
                                soalId = _examQuestionIds[i],
                                nomorSoal = i + 1,
                                pilihan = ""
                            )
                        )
                    }
                    mListExamNumber = newList
                }

                val index = data.nomorSoal - 1
                if (index in mListExamNumber.indices) {
                    // Pastikan soalId yang benar digunakan
                    val correctSoalId = _examQuestionIds.getOrNull(index) ?: data.soalId
                    val correctedData = data.copy(soalId = correctSoalId)

                    mListExamNumber[index] = correctedData
                    _listExamNumber.emit(mListExamNumber.toList())

                    // Save progress immediately after marking question
                    saveProgress()

                    // Log untuk debugging
                    android.util.Log.d("Exam", "Soal ${correctedData.nomorSoal} - Jawaban dipilih: ${correctedData.pilihan}, SoalId: ${correctedData.soalId}")
                    android.util.Log.d("Exam", "List exam terupdate: ${mListExamNumber.joinToString { "${it.nomorSoal}:${it.pilihan}:${it.soalId}" }}")
                } else {
                    android.util.Log.e("Exam", "Index out of bounds: ${data.nomorSoal - 1}, ukuran list: ${mListExamNumber.size}")

                    // Jika index out of bounds, coba expand list
                    if (index >= 0 && index < _examQuestionIds.size) {
                        while (mListExamNumber.size <= index) {
                            val missingIndex = mListExamNumber.size
                            val missingQuestionId = _examQuestionIds.getOrNull(missingIndex) ?: 0L
                            mListExamNumber.add(ExamNumber(
                                soalId = missingQuestionId,
                                nomorSoal = missingIndex + 1,
                                pilihan = ""
                            ))
                        }

                        // Sekarang coba lagi
                        val correctSoalId = _examQuestionIds.getOrNull(index) ?: data.soalId
                        val correctedData = data.copy(soalId = correctSoalId)
                        mListExamNumber[index] = correctedData
                        _listExamNumber.emit(mListExamNumber.toList())

                        // Save progress immediately after expanding and updating
                        saveProgress()

                        android.util.Log.d("Exam", "List expanded and updated - Soal ${correctedData.nomorSoal}: ${correctedData.pilihan}")
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("Exam", "Error di markQuestion: ${e.message}")
                e.printStackTrace()
            }
        }
    }
    fun saveProgress() {
        viewModelScope.launch {
            val data = ExamProgress(
                id = 0L,
                examId = examId,
                progress = ExamProgress.saveListExamNumber(mListExamNumber),
                timeLeft = elapsedTime,
                is_pending = false,
                isSubmitted = false
            )
            useCase.saveExamProgress(data)
            lastProgressData = data
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.cancel()
        if (this::timer.isInitialized) timer.cancel()
    }
}
