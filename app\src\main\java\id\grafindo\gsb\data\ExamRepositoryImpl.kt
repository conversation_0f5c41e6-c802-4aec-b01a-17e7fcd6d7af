package id.grafindo.gsb.data

import android.content.Context
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import id.grafindo.gsb.data.db.AppDatabase
import id.grafindo.gsb.data.remote.ApiService
import id.grafindo.gsb.data.remote.NetworkResult
import id.grafindo.gsb.data.remote.json.ErrorResponseJson
import id.grafindo.gsb.domain.model.Exam
import id.grafindo.gsb.domain.model.ExamProgress
import id.grafindo.gsb.domain.model.ExamSubmit
import id.grafindo.gsb.domain.repository.ExamRepository
import id.grafindo.gsb.ext.stringSuspend
import id.grafindo.gsb.util.mapper.asDomain
import id.grafindo.gsb.util.mapper.asEntity
import id.grafindo.gsb.util.mapper.asJson
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import id.grafindo.gsb.data.remote.json.BookExamResponseJson
import id.grafindo.gsb.data.remote.json.ExamAnswerJson
import id.grafindo.gsb.data.remote.json.ExamJson
import id.grafindo.gsb.data.remote.json.ExamQuestionJson
import id.grafindo.gsb.data.remote.json.TipeSoal
import id.grafindo.gsb.domain.model.ExamQuestion
import id.grafindo.gsb.domain.model.ExamAnswer
import id.grafindo.gsb.util.mapper.asDomain
import timber.log.Timber
import java.io.BufferedInputStream
import java.io.IOException
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

class ExamRepositoryImpl(
    private val context: Context,
    private val api: ApiService,
    private val db: AppDatabase,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) : ExamRepository {
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        coerceInputValues = true
    }

    override fun getExamBookDetail(bookId: Long) =
        db.bookDao.getBookDetail(bookId).map { it.asDomain() }

    override fun getExamList(bookId: Long): Flow<List<Exam>> = flow<List<Exam>> {
        try {
            Timber.d("Fetching exam list for book: $bookId - OPTIMIZED VERSION")
            val runtime = Runtime.getRuntime()
            val usedMemoryMB = (runtime.totalMemory() - runtime.freeMemory()) / (1024.0 * 1024.0)
            val maxMemoryMB = runtime.maxMemory() / (1024.0 * 1024.0)
            Timber.d("Memory usage before: ${String.format("%.2f", usedMemoryMB)} MB / ${String.format("%.2f", maxMemoryMB)} MB")

            val book = withContext(dispatcher) {
                db.bookDao.getBookDetailSuspend(bookId)?.asDomain()
                    ?: throw IllegalStateException("Buku tidak ditemukan")
            }

            val examFile = book.getExamFile(context)
            Timber.d("Exam file path: ${examFile.absolutePath}")
            if (!examFile.exists() || !examFile.canRead()) {
                throw IOException("File ujian tidak ditemukan atau tidak dapat dibaca.")
            }

            val fileSizeMB = examFile.length() / (1024.0 * 1024.0)
            Timber.d("Exam file size: ${String.format("%.2f", fileSizeMB)} MB")
            if (examFile.length() > 100 * 1024 * 1024L) {
                throw IOException("File ujian terlalu besar (${String.format("%.2f", fileSizeMB)} MB).")
            }

            // Log a larger preview of the file (up to 10KB around the problematic area)
            val previewLength = 10000L
            val previewStart = maxOf(0L, 791008L - previewLength / 2)
            val preview = StringBuilder()
            examFile.inputStream().use { inputStream: java.io.InputStream ->
                BufferedInputStream(inputStream).use { bufferedStream: BufferedInputStream ->
                    bufferedStream.skip(previewStart)
                    InputStreamReader(bufferedStream, StandardCharsets.UTF_8).use { reader: InputStreamReader ->
                        val buffer = CharArray(previewLength.toInt())
                        val charsRead = reader.read(buffer, 0, previewLength.toInt())
                        if (charsRead > 0) {
                            preview.append(buffer, 0, charsRead)
                        }
                    }
                }
            }
            Timber.d("JSON file preview around column 791008: ${preview.toString()}")

            val exams = mutableListOf<Exam>()
            Timber.d("Starting JSON parsing...")
            var examsFound = 0
            var invalidExams = 0
            var foundPaketUjian = false

            // Parse JSON using Gson JsonReader
            examFile.inputStream().use { inputStream: java.io.InputStream ->
                BufferedInputStream(inputStream).use { bufferedStream: BufferedInputStream ->
                    val reader = JsonReader(InputStreamReader(bufferedStream, StandardCharsets.UTF_8))
                    reader.isLenient = true
                    try {
                        reader.beginObject()
                        if (!reader.hasNext()) {
                            throw IOException("File ujian kosong atau tidak valid.")
                        }
                        while (reader.hasNext()) {
                            val key = reader.nextName()
                            Timber.d("Processing top-level key: $key")
                            when (key.lowercase()) {
                                "data" -> {
                                    reader.beginObject()
                                    while (reader.hasNext()) {
                                        val innerKey = reader.nextName()
                                        Timber.d("Processing data key: $innerKey")
                                        when (innerKey.lowercase()) {
                                            "mapel" -> {
                                                reader.beginObject()
                                                while (reader.hasNext()) {
                                                    val mapelKey = reader.nextName()
                                                    Timber.d("Processing mapel key: $mapelKey")
                                                    when (mapelKey.lowercase()) {
                                                        "paket_ujian", "paketujian" -> {
                                                            foundPaketUjian = true
                                                            if (reader.peek() != JsonToken.BEGIN_ARRAY) {
                                                                Timber.e("Expected BEGIN_ARRAY for paket_ujian, found ${reader.peek()}")
                                                                throw IOException("Invalid paket_ujian structure: expected array")
                                                            }
                                                            reader.beginArray()
                                                            while (reader.hasNext()) {
                                                                if (reader.peek() == JsonToken.NULL) {
                                                                    reader.skipValue()
                                                                    Timber.d("Skipped null exam object")
                                                                    invalidExams++
                                                                    continue
                                                                }
                                                                if (reader.peek() != JsonToken.BEGIN_OBJECT) {
                                                                    Timber.e("Expected BEGIN_OBJECT for exam at index $examsFound, found ${reader.peek()}")
                                                                    reader.skipValue()
                                                                    invalidExams++
                                                                    continue
                                                                }
                                                                try {
                                                                    // OPTIMIZED: Parse metadata only for exam list
                                                                    val examMetadata = parseExamMetadataJson(reader)
                                                                    val exam = Exam(
                                                                        id = examMetadata.id,
                                                                        namaPaket = examMetadata.namaPaket,
                                                                        mapelId = examMetadata.mapelId,
                                                                        waktu = examMetadata.waktu,
                                                                        petunjuk = examMetadata.petunjuk,
                                                                        soal = emptyList(), // Don't load soal for list
                                                                        isSelected = false
                                                                    )
                                                                    exams.add(exam)
                                                                    examsFound++
                                                                } catch (e: Exception) {
                                                                    Timber.e(e, "Failed to parse exam object at index $examsFound")
                                                                    invalidExams++
                                                                    reader.skipValue()
                                                                }
                                                            }
                                                            if (reader.peek() != JsonToken.END_ARRAY) {
                                                                Timber.e("Expected END_ARRAY for paket_ujian, found ${reader.peek()} at path ${reader.path}")
                                                                reader.skipValue()
                                                            }
                                                            reader.endArray()
                                                        }
                                                        else -> {
                                                            Timber.d("Skipping mapel key: $mapelKey")
                                                            reader.skipValue()
                                                        }
                                                    }
                                                }
                                                reader.endObject()
                                            }
                                            else -> {
                                                Timber.d("Skipping data key: $innerKey")
                                                reader.skipValue()
                                            }
                                        }
                                    }
                                    reader.endObject()
                                }
                                else -> {
                                    Timber.d("Skipping top-level key: $key")
                                    reader.skipValue()
                                }
                            }
                        }
                        if (reader.peek() != JsonToken.END_OBJECT) {
                            Timber.e("Expected END_OBJECT for root, found ${reader.peek()} at path ${reader.path}")
                            reader.skipValue()
                        }
                        reader.endObject()
                    } catch (e: Exception) {
                        Timber.e(e, "Error parsing JSON file")
                        throw IOException("Format file ujian tidak valid. Silakan unduh ulang ujian.", e)
                    } finally {
                        reader.close()
                    }
                }
            }

            if (!foundPaketUjian) {
                Timber.e("No 'paket_ujian' array found in file.")
                throw IOException("File ujian tidak berisi daftar ujian. Silakan unduh ulang.")
            }
            if (examsFound == 0) {
                Timber.e("No valid exams found. Parsed $invalidExams invalid exam objects. Possible causes: incorrect data types or malformed exam objects.")
                throw IOException("Tidak ada ujian yang valid ditemukan. File mungkin rusak atau format tidak sesuai. Silakan unduh ulang ujian.")
            }
            Timber.d("Successfully parsed $examsFound exams, skipped $invalidExams invalid exams")
            emit(exams.toList())
        } catch (e: OutOfMemoryError) {
            Timber.e(e, "Out of memory while parsing exam file")
            throw IOException("Memori tidak cukup untuk memproses file ujian. Silakan coba di perangkat dengan memori lebih besar.", e)
        } catch (e: Exception) {
            when (e) {
                is IOException -> Timber.e(e, "I/O error while reading exam file")
                is IllegalStateException -> Timber.e(e, "Invalid application state")
                else -> Timber.e(e, "Unexpected error while fetching exam list")
            }
            throw when (e) {
                is IOException -> e
                is IllegalStateException -> IOException("Data ujian tidak valid. Silakan coba lagi.", e)
                else -> IOException("Terjadi kesalahan saat memuat ujian. Silakan coba beberapa saat lagi.", e)
            }
        }
    }.flowOn(dispatcher)

    private fun parseExamJson(reader: JsonReader): ExamJson {
        var id: Long? = null
        var namaPaket: String? = null
        var mapelId: Long? = null
        var waktu: Long? = null
        var petunjuk: String? = null
        val soal = mutableListOf<ExamQuestionJson>()

        reader.beginObject()
        while (reader.hasNext()) {
            when (val key = reader.nextName()) {
                "id" -> id = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "nama_paket" -> namaPaket = try { reader.nextString() } catch (e: Exception) { reader.skipValue(); null }
                "mapel_id" -> mapelId = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "waktu" -> waktu = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "petunjuk" -> petunjuk = try { reader.nextString() } catch (e: Exception) { reader.skipValue(); null }
                "soal" -> {
                    if (reader.peek() == JsonToken.BEGIN_ARRAY) {
                        reader.beginArray()
                        while (reader.hasNext()) {
                            try {
                                soal.add(parseExamQuestionJson(reader))
                            } catch (e: Exception) {
                                Timber.e(e, "Failed to parse question object")
                                reader.skipValue()
                            }
                        }
                        reader.endArray()
                    } else {
                        Timber.d("Skipping unexpected soal value")
                        reader.skipValue()
                    }
                }
                else -> {
                    Timber.d("Skipping exam key: $key")
                    reader.skipValue()
                }
            }
        }
        reader.endObject()

        return ExamJson(
            id = id ?: throw IOException("Missing id in exam object"),
            namaPaket = namaPaket ?: throw IOException("Missing nama_paket in exam object"),
            mapelId = mapelId ?: throw IOException("Missing mapel_id in exam object"),
            waktu = waktu ?: throw IOException("Missing waktu in exam object"),
            petunjuk = petunjuk ?: throw IOException("Missing petunjuk in exam object"),
            soal = soal
        )
    }

    /**
     * OPTIMIZED: Parse exam metadata only (skip soal for better performance)
     */
    private fun parseExamMetadataJson(reader: JsonReader): ExamJson {
        var id: Long? = null
        var namaPaket: String? = null
        var mapelId: Long? = null
        var waktu: Long? = null
        var petunjuk: String? = null

        reader.beginObject()
        while (reader.hasNext()) {
            when (val key = reader.nextName()) {
                "id" -> id = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "nama_paket" -> namaPaket = try { reader.nextString() } catch (e: Exception) { reader.skipValue(); null }
                "mapel_id" -> mapelId = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "waktu" -> waktu = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "petunjuk" -> petunjuk = try { reader.nextString() } catch (e: Exception) { reader.skipValue(); null }
                "soal" -> {
                    // OPTIMIZED: Skip soal array entirely for better performance
                    Timber.d("Skipping soal array for exam metadata (performance optimization)")
                    reader.skipValue()
                }
                else -> {
                    Timber.d("Skipping exam key: $key")
                    reader.skipValue()
                }
            }
        }
        reader.endObject()

        return ExamJson(
            id = id ?: throw IOException("Missing id in exam object"),
            namaPaket = namaPaket ?: throw IOException("Missing nama_paket in exam object"),
            mapelId = mapelId ?: throw IOException("Missing mapel_id in exam object"),
            waktu = waktu ?: throw IOException("Missing waktu in exam object"),
            petunjuk = petunjuk ?: throw IOException("Missing petunjuk in exam object"),
            soal = emptyList() // Empty for metadata-only parsing
        )
    }

    private fun parseExamQuestionJson(reader: JsonReader): ExamQuestionJson {
        var id: Long? = null
        var soal: String? = null
        var tipeSoal: TipeSoal? = null
        var jawabanId: Long? = null
        var paketUjianId: Long? = null
        val dataJawaban = mutableListOf<ExamAnswerJson>()
        val jawabanMatchingRight = mutableListOf<ExamAnswerJson>()

        reader.beginObject()
        while (reader.hasNext()) {
            when (val key = reader.nextName()) {
                "id" -> id = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "soal" -> soal = try { reader.nextString() } catch (e: Exception) { reader.skipValue(); null }
                "tipe_soal" -> tipeSoal = try {
                    when (reader.nextString()) {
                        "menjodohkan" -> TipeSoal.Menjodohkan
                        "pilihan_ganda" -> TipeSoal.PilihanGanda
                        "pilihan_ganda_kompleks" -> TipeSoal.PilihanGandaKompleks
                        else -> { reader.skipValue(); null }
                    }
                } catch (e: Exception) { reader.skipValue(); null }
                "jawaban_id" -> jawabanId = if (reader.peek() == JsonToken.NULL) {
                    reader.nextNull()
                    null
                } else {
                    try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                }
                "paket_ujian_id" -> paketUjianId = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "data_jawaban" -> {
                    if (reader.peek() == JsonToken.BEGIN_ARRAY) {
                        reader.beginArray()
                        while (reader.hasNext()) {
                            try {
                                dataJawaban.add(parseExamAnswerJson(reader))
                            } catch (e: Exception) {
                                Timber.e(e, "Failed to parse answer object")
                                reader.skipValue()
                            }
                        }
                        reader.endArray()
                    } else {
                        Timber.d("Skipping unexpected data_jawaban value")
                        reader.skipValue()
                    }
                }
                "jawabanmatchingright" -> {
                    if (reader.peek() == JsonToken.BEGIN_ARRAY) {
                        reader.beginArray()
                        while (reader.hasNext()) {
                            try {
                                jawabanMatchingRight.add(parseExamAnswerJson(reader))
                            } catch (e: Exception) {
                                Timber.e(e, "Failed to parse matching answer object")
                                reader.skipValue()
                            }
                        }
                        reader.endArray()
                    } else {
                        Timber.d("Skipping unexpected jawabanmatchingright value")
                        reader.skipValue()
                    }
                }
                else -> {
                    Timber.d("Skipping question key: $key")
                    reader.skipValue()
                }
            }
        }
        reader.endObject()

        return ExamQuestionJson(
            id = id ?: throw IOException("Missing id in question object"),
            soal = soal ?: throw IOException("Missing soal in question object"),
            tipeSoal = tipeSoal ?: throw IOException("Missing tipe_soal in question object"),
            jawabanId = jawabanId,
            paketUjianId = paketUjianId ?: throw IOException("Missing paket_ujian_id in question object"),
            dataJawaban = dataJawaban,
            jawabanmatchingright = jawabanMatchingRight
        )
    }

    private fun parseExamAnswerJson(reader: JsonReader): ExamAnswerJson {
        var id: Long? = null
        var jawaban: String? = null
        var soalId: Long? = null

        reader.beginObject()
        while (reader.hasNext()) {
            when (val key = reader.nextName()) {
                "id" -> id = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                "jawaban" -> jawaban = try { reader.nextString() } catch (e: Exception) { reader.skipValue(); null }
                "soal_id" -> soalId = try { reader.nextLong() } catch (e: Exception) { reader.skipValue(); null }
                else -> {
                    Timber.d("Skipping answer key: $key")
                    reader.skipValue()
                }
            }
        }
        reader.endObject()

        return ExamAnswerJson(
            id = id ?: throw IOException("Missing id in answer object"),
            jawaban = jawaban ?: throw IOException("Missing jawaban in answer object"),
            soalId = soalId ?: throw IOException("Missing soal_id in answer object")
        )
    }

    override fun getExam(bookId: Long, examId: Long): Flow<Exam> = flow {
        try {
            Timber.d("Fetching exam with ID: $examId for book: $bookId from file")
            val runtime = Runtime.getRuntime()
            val usedMemoryMB = (runtime.totalMemory() - runtime.freeMemory()) / (1024.0 * 1024.0)
            val maxMemoryMB = runtime.maxMemory() / (1024.0 * 1024.0)
            Timber.d("Memory usage: ${String.format("%.2f", usedMemoryMB)} MB / ${String.format("%.2f", maxMemoryMB)} MB")

            val book = withContext(dispatcher) {
                db.bookDao.getBookDetailSuspend(bookId)?.asDomain()
                    ?: throw IllegalStateException("Buku tidak ditemukan")
            }

            val examFile = book.getExamFile(context)
            Timber.d("Exam file path: ${examFile.absolutePath}")
            if (!examFile.exists() || !examFile.canRead()) {
                throw IOException("File ujian tidak ditemukan atau tidak dapat dibaca.")
            }

            val fileSizeMB = examFile.length() / (1024.0 * 1024.0)
            Timber.d("Exam file size: ${String.format("%.2f", fileSizeMB)} MB")
            if (examFile.length() > 100 * 1024 * 1024L) {
                throw IOException("File ujian terlalu besar (${String.format("%.2f", fileSizeMB)} MB).")
            }

            // Log a larger preview around column 791008
            val previewLength = 10000L
            val previewStart = maxOf(0L, 791008L - previewLength / 2)
            val preview = StringBuilder()
            examFile.inputStream().use { inputStream: java.io.InputStream ->
                BufferedInputStream(inputStream).use { bufferedStream: BufferedInputStream ->
                    bufferedStream.skip(previewStart)
                    InputStreamReader(bufferedStream, StandardCharsets.UTF_8).use { reader: InputStreamReader ->
                        val buffer = CharArray(previewLength.toInt())
                        val charsRead = reader.read(buffer, 0, previewLength.toInt())
                        if (charsRead > 0) {
                            preview.append(buffer, 0, charsRead)
                        }
                    }
                }
            }
            Timber.d("JSON file preview around column 791008: ${preview.toString()}")

            var exam: Exam? = null
            var examIndex = 0
            examFile.inputStream().use { inputStream: java.io.InputStream ->
                BufferedInputStream(inputStream).use { bufferedStream: BufferedInputStream ->
                    val reader = JsonReader(InputStreamReader(bufferedStream, StandardCharsets.UTF_8))
                    reader.isLenient = true
                    try {
                        reader.beginObject()
                        if (!reader.hasNext()) {
                            throw IOException("File ujian kosong atau tidak valid.")
                        }
                        while (reader.hasNext()) {
                            val key = reader.nextName()
                            Timber.d("Processing top-level key: $key")
                            if (key.lowercase() == "data") {
                                reader.beginObject()
                                while (reader.hasNext()) {
                                    val innerKey = reader.nextName()
                                    Timber.d("Processing data key: $innerKey")
                                    if (innerKey.lowercase() == "mapel") {
                                        reader.beginObject()
                                        while (reader.hasNext()) {
                                            val mapelKey = reader.nextName()
                                            Timber.d("Processing mapel key: $mapelKey")
                                            if (mapelKey.lowercase() == "paket_ujian" || mapelKey.lowercase() == "paketujian") {
                                                if (reader.peek() != JsonToken.BEGIN_ARRAY) {
                                                    Timber.e("Expected BEGIN_ARRAY for paket_ujian, found ${reader.peek()} at path ${reader.path}")
                                                    throw IOException("Invalid paket_ujian structure: expected array")
                                                }
                                                reader.beginArray()
                                                while (reader.hasNext()) {
                                                    if (reader.peek() == JsonToken.NULL) {
                                                        Timber.d("Skipped null exam object at index $examIndex")
                                                        reader.skipValue()
                                                        examIndex++
                                                        continue
                                                    }
                                                    if (reader.peek() != JsonToken.BEGIN_OBJECT) {
                                                        Timber.e("Expected BEGIN_OBJECT for exam at index $examIndex, found ${reader.peek()} at path ${reader.path}")
                                                        reader.skipValue()
                                                        examIndex++
                                                        continue
                                                    }
                                                    try {
                                                        val examJson = parseExamJson(reader)
                                                        Timber.d("Parsed exam at index $examIndex with ID: ${examJson.id}")
                                                        if (examJson.id == examId) {
                                                            exam = examJson.asDomain()
                                                            break
                                                        }
                                                        examIndex++
                                                    } catch (e: Exception) {
                                                        Timber.e(e, "Failed to parse exam object at index $examIndex, path ${reader.path}")
                                                        reader.skipValue()
                                                        examIndex++
                                                    }
                                                }
                                                try {
                                                    if (reader.peek() != JsonToken.END_ARRAY) {
                                                        Timber.e("Expected END_ARRAY for paket_ujian, found ${reader.peek()} at path ${reader.path}")
                                                        while (reader.hasNext()) {
                                                            val token = reader.peek()
                                                            if (token == JsonToken.END_ARRAY) {
                                                                break
                                                            } else if (token == JsonToken.BEGIN_OBJECT || token == JsonToken.BEGIN_ARRAY) {
                                                                reader.skipValue()
                                                            } else {
                                                                reader.skipValue()
                                                            }
                                                        }
                                                    }
                                                    reader.endArray()
                                                } catch (e: Exception) {
                                                    Timber.e(e, "Error while trying to recover from malformed paket_ujian array")
                                                    try {
                                                        while (reader.peek() != JsonToken.END_ARRAY && reader.hasNext()) {
                                                            reader.skipValue()
                                                        }
                                                        if (reader.peek() == JsonToken.END_ARRAY) {
                                                            reader.endArray()
                                                        }
                                                    } catch (innerE: Exception) {
                                                        Timber.e(innerE, "Failed to recover from malformed JSON")
                                                        throw IOException("Invalid exam data format. Please try downloading the exam again.", innerE)
                                                    }
                                                }
                                            } else {
                                                Timber.d("Skipping mapel key: $mapelKey")
                                                reader.skipValue()
                                            }
                                        }
                                        reader.endObject()
                                        if (exam != null) break
                                    } else {
                                        Timber.d("Skipping data key: $innerKey")
                                        reader.skipValue()
                                    }
                                }
                                reader.endObject()
                                if (exam != null) break
                            } else {
                                Timber.d("Skipping top-level key: $key")
                                reader.skipValue()
                            }
                        }
                        if (reader.peek() != JsonToken.END_OBJECT) {
                            Timber.e("Expected END_OBJECT for root, found ${reader.peek()} at path ${reader.path}")
                            reader.skipValue()
                        }
                        reader.endObject()
                    } catch (e: Exception) {
                        Timber.e(e, "Error parsing JSON file for exam ID: $examId at index $examIndex, path ${reader.path}")
                        throw IOException("Format file ujian tidak valid di index $examIndex. Silakan unduh ulang ujian.", e)
                    } finally {
                        reader.close()
                    }
                }
            }

            if (exam == null) {
                Timber.e("Exam with ID $examId not found in file.")
                throw NoSuchElementException("Exam with ID $examId not found")
            }
            Timber.d("Successfully fetched exam ID: $examId")
            emit(exam!!)
        } catch (e: OutOfMemoryError) {
            Timber.e(e, "Out of memory while parsing exam file")
            throw IOException("Memori tidak cukup untuk memproses file ujian. Silakan coba di perangkat dengan memori lebih besar.", e)
        } catch (e: Exception) {
            when (e) {
                is IOException -> Timber.e(e, "I/O error while reading exam file")
                is IllegalStateException -> Timber.e(e, "Invalid application state")
                else -> Timber.e(e, "Unexpected error while fetching exam")
            }
            throw when (e) {
                is IOException -> e
                is IllegalStateException -> IOException("Data ujian tidak valid. Silakan coba lagi.", e)
                else -> IOException("Terjadi kesalahan saat memuat ujian. Silakan coba beberapa saat lagi.", e)
            }
        }
    }.flowOn(dispatcher)

    override suspend fun getLocalExamProgress(examId: Long) =
        db.examDao.getExamProgressByExamId(examId)?.asDomain()

    override suspend fun saveExamProgress(data: ExamProgress) {
        withContext(dispatcher) {
            db.examDao.insertExamProgress(data.asEntity())
        }
    }

    override fun submitExam(token: String, examId: Long, submitData: ExamSubmit) =
        flow {
            try {
                val response = api.checkExam(token, examId, submitData.asJson())
                if (response.isSuccessful) {
                    val data = response.body()?.data
                        ?: throw IllegalStateException("List is empty")
                    emit(NetworkResult.Success(data.asDomain()))
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString<ErrorResponseJson>(errorString)
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            } catch (e: IllegalStateException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override suspend fun getQuestionById(bookId: Long, examId: Long, questionId: Long): ExamQuestion {
        Timber.d("Fetching question with ID: $questionId from exam ID: $examId in book ID: $bookId")
        
        val book = withContext(dispatcher) {
            db.bookDao.getBookDetailSuspend(bookId)?.asDomain()
                ?: throw IllegalStateException("Buku tidak ditemukan")
        }

        val examFile = book.getExamFile(context)
        if (!examFile.exists() || !examFile.canRead()) {
            throw IOException("File ujian tidak ditemukan atau tidak dapat dibaca.")
        }

        var foundQuestion: ExamQuestion? = null
        
        examFile.inputStream().use { inputStream ->
            BufferedInputStream(inputStream).use { bufferedStream ->
                val reader = JsonReader(InputStreamReader(bufferedStream, StandardCharsets.UTF_8))
                reader.isLenient = true
                
                try {
                    reader.beginObject()
                    while (reader.hasNext()) {
                        val key = reader.nextName()
                        if (key.lowercase() == "data") {
                            reader.beginObject()
                            while (reader.hasNext()) {
                                val innerKey = reader.nextName()
                                if (innerKey.lowercase() == "mapel") {
                                    reader.beginObject()
                                    while (reader.hasNext()) {
                                        val mapelKey = reader.nextName()
                                        if (mapelKey.lowercase() == "paket_ujian" || mapelKey.lowercase() == "paketujian") {
                                            if (reader.peek() != JsonToken.BEGIN_ARRAY) {
                                                reader.skipValue()
                                                continue
                                            }
                                            reader.beginArray()
                                            while (reader.hasNext() && foundQuestion == null) {
                                                if (reader.peek() == JsonToken.NULL) {
                                                    reader.skipValue()
                                                    continue
                                                }
                                                if (reader.peek() != JsonToken.BEGIN_OBJECT) {
                                                    reader.skipValue()
                                                    continue
                                                }
                                                
                                                // Save the current position to return to it after checking the exam ID
                                                val jsonString = StringBuilder()
                                                val writer = java.io.StringWriter()
                                                val jsonWriter = com.google.gson.stream.JsonWriter(writer)
                                                
                                                // Start reading the exam object
                                                reader.beginObject()
                                                var currentExamId: Long? = null
                                                var isTargetExam = false
                                                
                                                // Read exam properties
                                                while (reader.hasNext()) {
                                                    val examKey = reader.nextName()
                                                    if (examKey == "id") {
                                                        currentExamId = reader.nextLong()
                                                        isTargetExam = (currentExamId == examId)
                                                    } else if (examKey == "soal" && isTargetExam) {
                                                        if (reader.peek() != JsonToken.BEGIN_ARRAY) {
                                                            reader.skipValue()
                                                            continue
                                                        }
                                                        reader.beginArray()
                                                        while (reader.hasNext() && foundQuestion == null) {
                                                            if (reader.peek() != JsonToken.BEGIN_OBJECT) {
                                                                reader.skipValue()
                                                                continue
                                                            }
                                                            
                                                            // Parse the question
                                                            val question = parseExamQuestionJson(reader)
                                                            if (question.id == questionId) {
                                                                foundQuestion = question.asDomain()
                                                                break
                                                            }
                                                        }
                                                        // Skip the rest of the questions array if we found our question
                                                        while (reader.hasNext()) {
                                                            reader.skipValue()
                                                        }
                                                        if (reader.peek() == JsonToken.END_ARRAY) {
                                                            reader.endArray()
                                                        }
                                                    } else {
                                                        reader.skipValue()
                                                    }
                                                }
                                                
                                                // Skip the rest of the exam object if we didn't find the question
                                                while (reader.hasNext()) {
                                                    reader.skipValue()
                                                }
                                                if (reader.peek() == JsonToken.END_OBJECT) {
                                                    reader.endObject()
                                                }
                                            }
                                            // Skip the rest of the exams array if we found our question
                                            while (reader.hasNext()) {
                                                reader.skipValue()
                                            }
                                            if (reader.peek() == JsonToken.END_ARRAY) {
                                                reader.endArray()
                                            }
                                        } else {
                                            reader.skipValue()
                                        }
                                    }
                                    if (reader.peek() == JsonToken.END_OBJECT) {
                                        reader.endObject()
                                    }
                                } else {
                                    reader.skipValue()
                                }
                            }
                            if (reader.peek() == JsonToken.END_OBJECT) {
                                reader.endObject()
                            }
                        } else {
                            reader.skipValue()
                        }
                    }
                    if (reader.peek() == JsonToken.END_OBJECT) {
                        reader.endObject()
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error parsing question with ID: $questionId from exam ID: $examId")
                    throw IOException("Gagal memuat pertanyaan. Silakan coba lagi.", e)
                } finally {
                    reader.close()
                }
            }
        }

        return foundQuestion ?: throw NoSuchElementException("Pertanyaan dengan ID $questionId tidak ditemukan dalam ujian ID $examId")
    }
}