package id.grafindo.gsb.domain.model

/**
 * Lightweight version of Exam for listing purposes.
 * Contains only metadata without the heavy soal list.
 */
data class ExamMetadata(
    val id: Long,
    val namaPaket: String,
    val mapelId: Long,
    val waktu: Long,
    val petunjuk: String,
    var isSelected: Boolean = false,
    var isSubmitted: Boolean = true,
    var isPending: Boolean = false,
) {
    /**
     * Convert to full Exam object when needed.
     * Note: soal list will be empty and should be loaded separately.
     */
    fun toExam(): Exam {
        return Exam(
            id = id,
            namaPaket = namaPaket,
            mapelId = mapelId,
            waktu = waktu,
            petunjuk = petunjuk,
            soal = emptyList(), // Will be loaded separately when needed
            isSelected = isSelected,
            isSubmitted = isSubmitted,
            isPending = isPending
        )
    }
}
