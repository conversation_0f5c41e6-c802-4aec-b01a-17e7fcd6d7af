package id.grafindo.gsb.ui.exam

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.google.android.material.appbar.MaterialToolbar
import id.grafindo.gsb.R
import id.grafindo.gsb.databinding.FragmentExamBinding
import id.grafindo.gsb.domain.model.ExamNumber
import id.grafindo.gsb.domain.model.ExamQuestion
import id.grafindo.gsb.ext.viewBinding
import id.grafindo.gsb.ui.MainActivity
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

class ExamFragment : Fragment(R.layout.fragment_exam) {
    companion object {
        const val ARGS_TIME_LEFT = "time_left"
    }

    private val binding by viewBinding<FragmentExamBinding>()
    private val ids by lazy {
        val args = ExamFragmentArgs.fromBundle(requireArguments())
        Pair(args.bookId, args.examId)
    }
    private val viewModel by viewModel<ExamViewModel> {
        parametersOf(ids.first, ids.second)
    }

    private val drawer get() = binding.drawerExam
    private val navView get() = binding.navViewExam
    private val layoutExam get() = binding.layoutExam
    private val toolbar get() = binding.layoutExam.toolbar

    private lateinit var header: View
    private lateinit var numberAdapter: ExamNumberAdapter
    private var questionCount = 0
    private var blankQuestionCount = 0
    private var isDoubtfulAnswered = false
    private lateinit var currentQuestion: ExamQuestion
    private lateinit var eventJob: Job

    // Delegates for handling different aspects
    private lateinit var questionRenderer: ExamQuestionRenderer
    private lateinit var answerHandler: ExamAnswerHandler

    // Store the latest examNumber to apply when currentQuestion is ready
    private var pendingExamNumber: ExamNumber? = null



    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (activity as MainActivity).hideBottomNavigation()
        setOrientationForDevice()
        setupToolbar()
        requireActivity().onBackPressedDispatcher.addCallback {
            handleBackNavigation()
        }
        initDrawer()
        initDelegates()
        setupObservers()
        handleTimeLeft()
    }

    private fun initDelegates() {
        questionRenderer = ExamQuestionRenderer(requireContext(), layoutExam)
        answerHandler = ExamAnswerHandler(
            context = requireContext(),
            layoutExam = layoutExam,
            questionRenderer = questionRenderer,
            onAnswerChanged = { examNumber -> viewModel.markQuestion(examNumber) }
        )
    }

    @SuppressLint("SourceLockedOrientationActivity")
    private fun setOrientationForDevice() {
        val isTablet =
            resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK >= Configuration.SCREENLAYOUT_SIZE_LARGE
        if (!isTablet) {
            (activity as MainActivity).requestedOrientation =
                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    private fun setupToolbar() {
        with(toolbar) {
            navigationIcon = ContextCompat.getDrawable(context, R.drawable.ic_arrow_left_alt)
            setNavigationOnClickListener { handleBackNavigation() }
            inflateMenu(R.menu.exam)
            setOnMenuItemClickListener {
                when (it.itemId) {
                    R.id.action_drawer -> toggleDrawer()
                }
                true
            }
        }
    }

    private fun setupObservers() {
        with(layoutExam) {
            viewModel.countDownText.observe(viewLifecycleOwner) { tvTimer.text = it }

            viewModel.indexQuestion.observe(viewLifecycleOwner, ::updateQuestionNavigationButtons)

            viewModel.activeQuestion.observe(viewLifecycleOwner, ::updateActiveQuestion)

            viewModel.activeNumber.observe(viewLifecycleOwner, ::updateActiveNumber)

            viewModel.listExamNumber.observe(viewLifecycleOwner, ::updateExamNumberList)

            btnPrev.setOnClickListener {
                viewModel.prevQuestion()
            }

            btnNext.setOnClickListener {
                viewModel.nextQuestion()
            }

            btnFinish.setOnClickListener { showFinishExamDialog() }
        }
    }

    private fun updateQuestionNavigationButtons(index: Int) {
        resetPrevNextButton()
        layoutExam.tvQuestionNum.text = getString(R.string.text_exam_question_number, index)
        if (index == 1) togglePrevButton(true)
        else if (index == questionCount) toggleNextButton(true)
    }

    private fun updateActiveQuestion(data: ExamQuestion) {
        android.util.Log.d("ExamFragment", "updateActiveQuestion - question: id=${data.id}, tipeSoal=${data.tipeSoal}")

        currentQuestion = data
        questionRenderer.renderQuestion(data)
        answerHandler.setupAnswerOptions(data)

        // If there's a pending examNumber, apply it now that currentQuestion is ready
        pendingExamNumber?.let { examNumber ->
            android.util.Log.d("ExamFragment", "Applying pending examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}")
            answerHandler.updateActiveNumber(examNumber, currentQuestion)
            pendingExamNumber = null
        }
    }

    private fun updateActiveNumber(examNumber: ExamNumber) {
        if (!::currentQuestion.isInitialized) {
            android.util.Log.d("ExamFragment", "updateActiveNumber called but currentQuestion not initialized yet - storing as pending")
            pendingExamNumber = examNumber
            return
        }

        android.util.Log.d("ExamFragment", "updateActiveNumber - examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}, pilihan=${examNumber.pilihan}")
        android.util.Log.d("ExamFragment", "updateActiveNumber - currentQuestion: id=${currentQuestion.id}, tipeSoal=${currentQuestion.tipeSoal}")

        answerHandler.updateActiveNumber(examNumber, currentQuestion)
    }





    private fun updateExamNumberList(list: List<ExamNumber>) {
        numberAdapter.submitList(list.map { it.copy() })
        questionCount = list.size
        blankQuestionCount = list.count { it.pilihan.isEmpty() }
        isDoubtfulAnswered = list.any { it.ragu }
    }

    private fun showFinishExamDialog() {
        val messageResId = when {
            blankQuestionCount != 0 -> R.string.text_dialog_end_exam_alt_subtitle
            isDoubtfulAnswered -> R.string.text_dialog_end_exam_doubt_subtitle
            viewModel.isTooFastExam() -> R.string.text_dialog_end_exam_speed_subtitle
            else -> R.string.text_dialog_end_exam_subtitle
        }

        MaterialDialog(requireContext()).show {
            title(R.string.text_dialog_end_exam_title)
            message(text = getString(messageResId, blankQuestionCount))
            positiveButton(R.string.text_dialog_yes) {
                viewModel.finishExam()
                navigateToExamSubmit()
            }
            negativeButton(R.string.text_dialog_cancel)
        }
    }

    private fun handleTimeLeft() {
        findNavController().currentBackStackEntry?.savedStateHandle
            ?.getLiveData<Long>(ARGS_TIME_LEFT)
            ?.observe(viewLifecycleOwner) {
                viewModel.continueExam(it)
            }
    }

    override fun onStart() {
        super.onStart()
        eventJob = viewModel.eventsFlow.onEach { handleEvent(it) }
            .launchIn(viewLifecycleOwner.lifecycleScope)
    }

    private fun handleEvent(event: ExamViewModel.UIEvent) {
        when (event) {
            ExamViewModel.UIEvent.ExamReady -> setupExamReady()
            ExamViewModel.UIEvent.StartExamPrompt -> viewModel.startExam()
            ExamViewModel.UIEvent.ResumeExamPrompt -> viewModel.resumeExam()
            ExamViewModel.UIEvent.StopExam -> showStopExamDialog()
        }
    }

    private fun setupExamReady() {
        viewModel.book.observe(viewLifecycleOwner) { book ->
            book?.let {
                toolbar.apply {
                    setSubtitleTextAppearance(
                        requireContext(),
                        R.style.TextAppearance_AppCompat_Small
                    )
                    setSubtitleTextColor(ContextCompat.getColor(requireContext(), R.color.white))
                    isTitleCentered = true
                    isSubtitleCentered = true
                    title = viewModel.getExamName()
                    subtitle = it.ebook.judul
                }
            }
        }
    }

    private fun showStopExamDialog() {
        MaterialDialog(requireContext()).show {
            title(R.string.text_dialog_stop_exam_title)
            message(R.string.text_dialog_stop_exam_subtitle)
            positiveButton(R.string.text_dialog_ok) {
                viewModel.saveProgress()
                navigateToExamSubmit()
            }
            cancelable(false)
        }
    }

    override fun onStop() {
        super.onStop()
        eventJob.cancel()
    }

    private fun initDrawer() {
        numberAdapter = ExamNumberAdapter(requireContext()) {
            viewModel.jumpToQuestion(it.nomorSoal)
            toggleDrawer()
        }

        header = navView.getHeaderView(0)
        header.findViewById<RecyclerView>(R.id.list).adapter = numberAdapter
        header.findViewById<MaterialToolbar>(R.id.toolbar).apply {
            navigationIcon = ContextCompat.getDrawable(context, R.drawable.ic_menu_open)
            setNavigationOnClickListener { toggleDrawer() }
        }
    }

    private fun toggleDrawer() {
        if (drawer.isDrawerOpen(GravityCompat.END)) {
            drawer.closeDrawer(GravityCompat.END)
        } else {
            drawer.openDrawer(GravityCompat.END)
        }
    }

    private fun resetPrevNextButton() {
        togglePrevButton(false)
        toggleNextButton(false)
    }

    private fun togglePrevButton(isDisabled: Boolean) {
        layoutExam.btnPrev.apply {
            setBackgroundResource(if (isDisabled) R.drawable.btn_exam_arrow_prev_un else R.drawable.btn_exam_arrow_prev)
            isClickable = !isDisabled
        }
    }

    private fun toggleNextButton(isDisabled: Boolean) {
        with(layoutExam) {
            btnNext.apply {
                setBackgroundResource(if (isDisabled) R.drawable.btn_exam_arrow_next_un else R.drawable.btn_exam_arrow_next)
                isClickable = !isDisabled
            }
            btnFinish.visibility = if (isDisabled) View.VISIBLE else View.GONE
        }
    }

    private fun handleBackNavigation() {
        MaterialDialog(requireContext()).show {
            title(R.string.text_dialog_exit_exam_title)
            message(R.string.text_dialog_exit_exam_subtitle)
            positiveButton(R.string.text_dialog_yes) {
                viewModel.saveProgress()
                findNavController().popBackStack()
            }
            negativeButton(R.string.text_dialog_cancel)
        }
    }

    private fun navigateToExamSubmit() {
        findNavController().navigate(
            ExamFragmentDirections.actionExamToExamSubmit(ids.first, ids.second)
        )
    }
}
