package id.grafindo.gsb.data

import id.grafindo.gsb.App
import id.grafindo.gsb.data.preference.AppPreference
import id.grafindo.gsb.data.preference.model.ActivationPreference
import id.grafindo.gsb.data.preference.model.UserPreference
import id.grafindo.gsb.data.remote.ApiService
import id.grafindo.gsb.data.remote.NetworkResult
import id.grafindo.gsb.data.remote.json.ConfigBodyJson
import id.grafindo.gsb.data.remote.json.ErrorResponseAltJson
import id.grafindo.gsb.data.remote.json.ErrorResponseJson
import id.grafindo.gsb.domain.enums.ActivationStatus
import id.grafindo.gsb.domain.enums.AuthStatus
import id.grafindo.gsb.domain.enums.CustomerCareStatus
import id.grafindo.gsb.domain.enums.ForgotPasswordStatus
import id.grafindo.gsb.domain.enums.UpdatePasswordStatus
import id.grafindo.gsb.domain.model.Activation
import id.grafindo.gsb.domain.model.CodeCheck
import id.grafindo.gsb.domain.model.Config
import id.grafindo.gsb.domain.model.CustomerCare
import id.grafindo.gsb.domain.model.ForgotPassword
import id.grafindo.gsb.domain.model.Login
import id.grafindo.gsb.domain.model.OtpCheck
import id.grafindo.gsb.domain.model.UpdatePassword
import id.grafindo.gsb.domain.repository.AuthRepository
import id.grafindo.gsb.ext.stringSuspend
import id.grafindo.gsb.util.mapper.asDomain
import id.grafindo.gsb.util.mapper.asJson
import id.grafindo.gsb.util.mapper.asRemote
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import timber.log.Timber
import java.io.File
import java.io.IOException

class AuthRepositoryImpl(
    private val api: ApiService,
    private val prefs: AppPreference,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO,
) : AuthRepository {
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    override fun login(loginData: Login): Flow<NetworkResult<AuthStatus>> =
        flow {
            try {
                val response = api.login(loginData.asRemote())
                if (response.isSuccessful) {
                    val data = response.body()?.loginData
                        ?: throw IllegalStateException("List is empty")
                    if (data.loginStatus != "login") {
                        val userPref = UserPreference(
                            idSiswa = data.idSiswa,
                            nama = data.namaSiswa,
                            email = data.email,
                            password = loginData.password,
                            token = "Bearer " + data.token,
                            sekolah = data.namaSekolah,
                            grup = data.namaGrup
                        )
                        prefs.updateUserPreference(userPref)
                        prefs.updateStateLoggedIn()
                        App.instance.reloadDatabaseModule()
                        Timber.d("Token user : $userPref.token")
                        emit(NetworkResult.Success(AuthStatus.LOGGED_IN))
                    } else {
                        emit(NetworkResult.Success(AuthStatus.LOGGED_IN_OTHER_DEVICE))
                    }
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            } catch (e: IllegalStateException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun logout(token: String, data: Login): Flow<NetworkResult<AuthStatus>> =
        flow {
            try {
                Timber.d("MAU LOGOUT")
                val response = api.logout(token, data.asRemote())
                Timber.d("MAU LOGOUT : ${response.code()}")

                if (response.code() == 502) {
                    throw IOException("HTML")
                }

                if (response.isSuccessful) {
                    prefs.wipeUserPreference()
                    prefs.updateStateFirstTimeSync(true)
                    App.instance.reloadDatabaseModule()
                    emit(NetworkResult.Success(AuthStatus.LOGGED_OUT))
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )


                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun deleteAccount(token: String): Flow<NetworkResult<Pair<AuthStatus, String>>> =
        flow {
            try {
                Timber.d("MY TOKEN $token")
                val response = api.deleteAccount(token)

                Timber.d("$response")

                if (response.code() == 502) {
                    throw IOException("HTML")
                }

                Timber.d("DELETE : ${response.code()}")

                if (response.isSuccessful) {
                    Timber.d("$response")

                    prefs.wipeUserPreference()
                    prefs.updateStateFirstTimeSync(true)
                    App.instance.reloadDatabaseModule()

                    emit(
                        NetworkResult.Success(
                            Pair(
                                AuthStatus.LOGGED_OUT,
                                response.body()?.data ?: ""
                            )
                        )
                    )
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                Timber.d("DELETE : ${e.message}")
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun activateCode(activationData: CodeCheck): Flow<NetworkResult<ActivationStatus>> =
        flow {
            try {
                val response = api.authActivation(activationData.asRemote())
                if (response.isSuccessful) {
                    prefs.wipeUserPreference()
                    emit(NetworkResult.Success(ActivationStatus.ACTIVATION_PHASE_1_DONE))
                    val userPref = ActivationPreference(
                        email = activationData.email,
                        otp = "",
                        activationCode = activationData.activationCode,
                        name = ""
                    )
                    prefs.updateActivationAccount(userPref)
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun activateOTP(activationData: OtpCheck): Flow<NetworkResult<ActivationStatus>> =
        flow {
            try {
                val response = api.authActivationOTP(activationData.asRemote())
                if (response.isSuccessful) {
                    val data = response.body()?.data
                        ?: throw IllegalStateException("Response body kosong")
                    prefs.wipeUserPreference()
                    emit(NetworkResult.Success(ActivationStatus.ACTIVATION_PHASE_2_DONE))
                    val userPref = ActivationPreference(
                        email = activationData.email,
                        otp = activationData.otp,
                        activationCode = activationData.activationCode,
                        name = data.namaSiswa
                    )
                    prefs.updateActivationAccount(userPref)
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun activatePassword(activationData: Activation):
        Flow<NetworkResult<ActivationStatus>> =
        flow {
            try {
                val response = api.userSetPassword(activationData.asRemote())

                if (response.isSuccessful) {
                    val data = response.body()?.data
                        ?: throw IllegalStateException("List is empty")

                    prefs.wipeUserPreference()
                    emit(NetworkResult.Success(ActivationStatus.ACTIVATION_PHASE_3_DONE))
                    val userPref = ActivationPreference(
                        email = activationData.email,
                        otp = activationData.otp,
                        activationCode = activationData.activationCode,
                        name = data.namaSiswa
                    )
                    prefs.updateActivationAccount(userPref)
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun checkEmail(forgotPasswordData: ForgotPassword):
        Flow<NetworkResult<ForgotPasswordStatus>> =
        flow {
            try {
                Timber.d("Data is $forgotPasswordData")
                val response = api.authForgotPassword(forgotPasswordData.asRemote())
                if (response.isSuccessful) {
                    val data =
                        response.body() ?: throw IllegalStateException("Response body kosong")

                    if (data.status) {
                        emit(NetworkResult.Success(ForgotPasswordStatus.PHASE_1_DONE))
                    } else {
                        throw IOException(data.message)
                    }
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun checkOtp(forgotPasswordData: ForgotPassword):
        Flow<NetworkResult<ForgotPasswordStatus>> =
        flow {
            try {
                Timber.d("Data is $forgotPasswordData")
                val response = api.authForgotPasswordOtp(forgotPasswordData.asRemote())
                if (response.isSuccessful) {
                    val data =
                        response.body() ?: throw IllegalStateException("Response body kosong")

                    if (data.status) {
                        emit(NetworkResult.Success(ForgotPasswordStatus.PHASE_2_DONE))
                    } else {
                        throw IOException(data.message)
                    }
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson = json.decodeFromString(
                        ErrorResponseJson.serializer(),
                        errorString
                    )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun setNewPassword(forgotPasswordData: ForgotPassword):
        Flow<NetworkResult<ForgotPasswordStatus>> =
        flow {
            try {
                Timber.d("Data is $forgotPasswordData")
                val response = api.authForgotPasswordUpdate(forgotPasswordData.asRemote())
                if (response.isSuccessful) {
                    val data =
                        response.body() ?: throw IllegalStateException("Response body kosong")
                    if (data.status) {
                        emit(NetworkResult.Success(ForgotPasswordStatus.PHASE_3_DONE))
                    } else {
                        throw IOException(data.message)
                    }
                } else {
                    val errorString = response.errorBody()!!.stringSuspend()
                    val errorJson =
                        json.decodeFromString(
                            ErrorResponseJson.serializer(),
                            errorString
                        )
                    throw IOException(errorJson.message)
                }
            } catch (e: IOException) {
                emit(NetworkResult.Error(e))
            }
        }.flowOn(dispatcher)

    override fun updatePassword(
        token: String,
        updatePasswordData: UpdatePassword,
    ): Flow<NetworkResult<UpdatePasswordStatus>> = flow {
        try {
            val response = api.userUpdatePassword(token, updatePasswordData.asRemote())
            if (response.isSuccessful) {
                prefs.updateUserPassword(updatePasswordData.newPassword)
                emit(NetworkResult.Success(UpdatePasswordStatus.SUCCESS))
            } else {
                val errorString = response.errorBody()!!.stringSuspend()
                val errorJson = json.decodeFromString(ErrorResponseJson.serializer(), errorString)
                throw IOException(errorJson.message)
            }
        } catch (e: IOException) {
            emit(NetworkResult.Error(e))
        }
    }.flowOn(dispatcher)

    override fun getConfig(name: String): Flow<NetworkResult<Config>> = flow {
        try {
            val response = api.getConfig(ConfigBodyJson(name))
            if (response.isSuccessful) {
                val body = response.body() ?: throw IllegalStateException("Response body kosong")
                emit(NetworkResult.Success(body.data.asDomain()))
            } else {
                val errorString = response.errorBody()!!.stringSuspend()
                val errorJson = json.decodeFromString(ErrorResponseJson.serializer(), errorString)
                throw IOException(errorJson.message)
            }
        } catch (e: IOException) {
            emit(NetworkResult.Error(e))
        }
    }

    override fun submitIssue(
        token: String,
        customerCareData: CustomerCare,
        screenshotList: List<File>,
    ): Flow<NetworkResult<CustomerCareStatus>> = flow {
        try {
            val screenshotData = prepareImagePartData(screenshotList)
            val response = api.submitIssue(
                token = token,
                partMap = customerCareData.asJson().asPartMap(),
                screenshots = screenshotData
            )
            if (response.isSuccessful) {
                response.body() ?: throw IllegalStateException("Response body kosong")
                emit(NetworkResult.Success(CustomerCareStatus.SUCCESS))
            } else {
                val errorString = response.errorBody()!!.stringSuspend()
                val errorJson =
                    json.decodeFromString(ErrorResponseAltJson.serializer(), errorString)
                throw IOException(errorJson.data)
            }
        } catch (e: IOException) {
            emit(NetworkResult.Error(e))
        }
    }

    private fun prepareImagePartData(fileList: List<File>): List<MultipartBody.Part> {
        val partList = ArrayList<MultipartBody.Part>()
        fileList.forEach { file ->
            val requestBody = file.asRequestBody("image/png".toMediaTypeOrNull())
            partList.add(
                MultipartBody.Part.createFormData(
                    "screenshots",
                    file.name,
                    requestBody,
                )
            )
        }
        return partList
    }
}
